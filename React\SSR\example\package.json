{"name": "react_origin_source", "version": "0.1.0", "private": true, "dependencies": {"axios": "^1.8.3", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "^4.21.2", "express-http-proxy": "^2.1.1", "express-session": "^1.18.1", "isomorphic-style-loader": "^5.4.0", "jquery": "^3.7.1", "react": "16.8.6", "react-dom": "16.8.6", "react-redux": "^8.1.3", "react-router": "^5.3.4", "react-router-config": "^5.1.1", "react-router-dom": "^5.3.4", "react-scripts": "5.0.1", "redux": "^5.0.1", "redux-logger": "^3.0.6", "redux-thunk": "^3.1.0", "webpack-node-externals": "^3.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev:build:client": "webpack --config webpack.client.js --watch", "dev:build:server": "webpack --config webpack.server.js --watch", "dev:start": "nodemon build/server.js", "dev": "npm-run-all --parallel dev:**", "api": "nodemon api/index.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"nodemon": "^3.1.9", "npm-run-all": "^4.1.5", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1"}}